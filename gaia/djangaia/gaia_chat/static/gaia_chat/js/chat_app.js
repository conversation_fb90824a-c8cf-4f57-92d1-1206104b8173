// Chat App JavaScript
// This file contains the Vue.js application logic for the chat interface

/* -----------------------------------------------------------
 *  Composite-pattern renderer (no Markdown dependency)
 *  wired into the Vue instance as methods.
 * ----------------------------------------------------------- */

/* ---------- Composite hierarchy (outside Vue) ---------- */
class Node {
    render () { throw new Error('Subclasses must implement render()'); }
}

class TextNode extends Node {
    constructor (text) { super(); this.text = text; }
    render () { return ChatHelpers.plainTextToHtml(this.text); }
}

class ListNode extends Node {
    constructor () { super(); this.children = []; }
    add (child)  { this.children.push(child); }
    render () {
        return `<ul>${this.children.map(c => `<li>${c.render()}</li>`).join('')}</ul>`;
  }
}

class TableNode extends Node {
  constructor ({ title = '', columns = [] } = {}) {
    super();
    this.title   = title;
    this.columns = columns;
    this.rows    = [];               // array<array<Node>>
  }
  addRow (...cells) { this.rows.push(cells); }

  render () {
    let html = '';

    if (this.title) {
        html += `<h4 style="margin-bottom:10px;color:#333;">${ChatHelpers.escapeHtml(this.title)}</h4>`;
    }

    html += '<table style="border-collapse:collapse;width:100%;margin:10px 0;border:1px solid #ddd;">';

    if (this.columns.length) {
        html += '<thead><tr>';
        html += this.columns
            .map(c => `<th style="border:1px solid #ddd;padding:8px;background:#f5f5f5;text-align:left;font-weight:bold;">${ChatHelpers.escapeHtml(c)}</th>`)
            .join('');
        html += '</tr></thead>';
    }

    html += '<tbody>';
    this.rows.forEach((row, i) => {
        const bg = i % 2 ? '#f9f9f9' : '#ffffff';
        html += `<tr style="background-color:${bg};">`;
        html += row
            .map(cell => `<td style="border:1px solid #ddd;padding:8px;">${cell.render()}</td>`)
            .join('');
        html += '</tr>';
    });
    html += '</tbody></table>';

    return html;
  }
}

class SVGNode extends Node {
  constructor ({ svg_content, chart_type = 'Chart', title = '' } = {}) {
    super();
    this.svg_content = svg_content;
    this.chart_type = chart_type;
    this.title = title;
  }

  render () {
    if (!this.svg_content) {
        return '<div class="error">No SVG content provided</div>';
    }

    const displayTitle = this.title || this.chart_type.replace('_', ' ').toUpperCase();

    // Debug logging
    console.log('🎨 SVGNode rendering with content length:', this.svg_content.length);
    console.log('🎨 SVG content preview (first 200 chars):', this.svg_content.substring(0, 200));
    console.log('🎨 SVG content ends with:', this.svg_content.substring(this.svg_content.length - 50));

    return `
        <div class="svg-chart-content" style="
            margin: 15px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
            text-align: center;
            overflow: auto;
        ">
            <div class="svg-title" style="
                font-size: 16px;
                color: #333;
                margin-bottom: 15px;
                font-weight: bold;
            ">${ChatHelpers.escapeHtml(displayTitle)}</div>
            <div class="svg-wrapper" style="
                max-width: 100%;
                overflow: auto;
                background: white;
                border-radius: 4px;
                padding: 10px;
            ">${this.svg_content}</div>
        </div>`;
  }
}


// Configure Vue to use [[ ]] for interpolation to avoid conflict with Django templates
Vue.config.delimiters = ['[[', ']]'];

// Initialize the Vue application
function initializeChatApp(djangoData) {

    /* helper namespace referenced by Node classes */
    const ChatHelpers = {
        /* ---------- basic escaping / plain-text → HTML ---------- */
        escapeHtml (str = '') {
        return str
            .replace(/&/g,  '&amp;')
            .replace(/</g,  '&lt;')
            .replace(/>/g,  '&gt;')
            .replace(/"/g,  '&quot;')
            .replace(/'/g,  '&#39;');
        },
        plainTextToHtml (text) {
        return ChatHelpers.escapeHtml(text).replace(/\r?\n/g, '<br>');
        }
    };

    /* expose helpers for Node classes */
    window.ChatHelpers = ChatHelpers;


    new Vue({
        el: '#app',
        delimiters: ['[[', ']]'],
        data: {
            // LLM settings
            providers: [],
            models: [],
            selectedProvider: 'mcp-http',
            selectedModel: '',
            hasOpenAIKey: djangoData.hasOpenAIKey,
            hasAnthropicKey: djangoData.hasAnthropicKey,
            isSettingLLM: false,
            mcpProtocol: '',  // Will store the MCP protocol (SSE or HTTP)

            // Conversations
            conversations: [],
            activeConversation: null,
            newConversationTitle: '',
            isCreatingConversation: false,
            isLoadingConversation: false,
            isDeletingConversation: null,

            // Messaging
            userInput: '',
            isSendingMessage: false,
            isStreamingMessage: false,
            streamingProgress: [],
            streamingInfo: [],
            toolResult: null,

            // Errors
            error: null,

            // Chat context
            chatContext: djangoData.chatContext,

            // Navigation - will be set in mounted()
            activeNavSection: null,

            // Pagination
            currentPage: 1,
            itemsPerPage: 100,  // Show 100 companies per page

            // Dynamic loading
            isLoading: false,
            loadingError: null,
            searchTerm: '',
            totalCompanies: 0,
            totalPages: 0,

            // Add this to the data object
            sidebarChatContainer: null,

            // Store Django data for use in methods
            djangoData: djangoData
        },
        computed: {
            // Dynamic pagination computed properties
            computedTotalPages() {
                if (this.chatContext && this.chatContext.dynamic_loading) {
                    return this.totalPages; // From server response
                }
                if (!this.chatContext || !this.chatContext.data) return 1;
                return Math.ceil(this.chatContext.data.length / this.itemsPerPage);
            },
            paginatedCompanies() {
                if (this.chatContext && this.chatContext.dynamic_loading) {
                    return this.chatContext.data; // Already paginated from server
                }
                if (!this.chatContext || !this.chatContext.data) return [];
                const start = (this.currentPage - 1) * this.itemsPerPage;
                const end = start + this.itemsPerPage;
                return this.chatContext.data.slice(start, end);
            },
            displayTotalCompanies() {
                if (this.chatContext && this.chatContext.dynamic_loading) {
                    return this.totalCompanies; // From server response
                }
                return this.chatContext ? this.chatContext.data.length : 0;
            }
        },
        mounted() {
            // Set active navigation section based on current URL
            this.activeNavSection = this.getActiveNavSectionFromUrl();

            // Test JSON highlighting function
            //console.log('Testing JSON highlighting...');
            const testJson = '{"columns": ["id", "name"], "data": [[1, "Alice"], [2, "Bob"]]}';
            const highlighted = this.highlightJson(JSON.stringify(JSON.parse(testJson), null, 4));
            //console.log('Highlighted JSON:', highlighted);

            this.fetchProviders();
            this.fetchConversations();

            // Check if there's a conversation ID in the URL and load it
            this.checkAndLoadConversationFromUrl();

            // Load company data if dynamic loading is enabled
            if (this.chatContext && this.chatContext.dynamic_loading) {
                console.log('Dynamic loading enabled, calling loadCompanyData()');
                this.loadCompanyData();
            } else {
                console.log('Dynamic loading disabled or no chatContext', {
                    chatContext: this.chatContext,
                    dynamic_loading: this.chatContext?.dynamic_loading
                });
            }

            // Set reference to sidebar chat container
            this.$nextTick(() => {
                this.sidebarChatContainer = document.querySelector('.chat-messages-container');
            });
        },
        updated() {
            this.scrollToBottom();
        },
        methods: {
            // Navigation
            getActiveNavSectionFromUrl() {
                const path = window.location.pathname;
                if (path.includes('/companies/')) {
                    return 'companies';
                } else if (path.includes('/people/')) {
                    return 'people';
                } else if (path.includes('/deals/')) {
                    return 'deals';
                } else {
                    return null; // No section active for base chat page
                }
            },

            setActiveNavSection(section) {
                this.activeNavSection = section;
                console.log('Active navigation section:', section);
                // You can add logic here to filter content or change views based on the section
            },

            // URL handling
            checkAndLoadConversationFromUrl() {
                // Check if there's a conversation parameter in the URL
                const urlParams = new URLSearchParams(window.location.search);
                const conversationId = urlParams.get('conversation');

                if (conversationId) {
                    console.log('Found conversation ID in URL:', conversationId);
                    // Wait a bit for conversations to be fetched, then load the conversation
                    setTimeout(() => {
                        this.loadConversation(conversationId);
                    }, 500);
                }
            },

            // Dynamic company data loading
            async loadCompanyData(page = null, search = null) {
                if (page === null) page = this.currentPage;
                if (search === null) search = this.searchTerm;

                this.isLoading = true;
                this.loadingError = null;

                try {
                    const params = {
                        page: page,
                        per_page: this.itemsPerPage
                    };

                    if (search && search.trim()) {
                        params.search = search.trim();
                    }

                    console.log('Making API call to /gaia_chat/api/company-data/ with params:', params);
                    const response = await axios.get('/gaia_chat/api/company-data/', { params });
                    const data = response.data;
                    console.log('API response:', data);

                    // Update chat context with new data
                    if (this.chatContext) {
                        // Use Vue.set to ensure reactivity
                        this.$set(this.chatContext, 'data', data.data);
                        this.$set(this.chatContext, 'title', data.title);
                        this.$set(this.chatContext, 'description', data.description);
                    }

                    // Update pagination info
                    this.totalCompanies = data.pagination.total_companies;
                    this.totalPages = data.pagination.total_pages;
                    this.currentPage = data.pagination.current_page;

                    console.log(`Loaded page ${this.currentPage}/${this.totalPages} with ${data.data.length} companies`);
                    console.log('Updated chatContext.data:', this.chatContext.data);
                    console.log('paginatedCompanies computed property:', this.paginatedCompanies);

                } catch (error) {
                    console.error('Error loading company data:', error);
                    this.loadingError = error.response?.data?.error || 'Failed to load company data';
                } finally {
                    this.isLoading = false;
                }
            },

            // Search companies
            async searchCompanies() {
                this.currentPage = 1; // Reset to first page when searching
                await this.loadCompanyData(1, this.searchTerm);
            },

            // Pagination methods
            async goToPage(page) {
                if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                    await this.loadCompanyData(page);
                }
            },

            async goToFirstPage() {
                await this.goToPage(1);
            },

            async goToPreviousPage() {
                await this.goToPage(this.currentPage - 1);
            },

            async goToNextPage() {
                await this.goToPage(this.currentPage + 1);
            },

            async goToLastPage() {
                await this.goToPage(this.totalPages);
            },

            // API calls
            async fetchProviders() {
                try {
                    const response = await axios.get('/gaia_chat/api/llm/providers/');
                    this.providers = response.data.providers;

                    // Set default provider to MCP HTTP if available, otherwise fallback to mock
                    const mcpHttpProvider = this.providers.find(p => p.id === 'mcp-http');
                    if (mcpHttpProvider) {
                        this.selectedProvider = 'mcp-http';
                    } else {
                        this.selectedProvider = 'mock';
                    }

                    await this.onProviderChange();
                } catch (error) {
                    console.error('Error fetching providers:', error);
                }
            },

            async onProviderChange() {
                try {
                    const response = await axios.get('/gaia_chat/api/llm/models/', {
                        params: { provider: this.selectedProvider }
                    });
                    this.models = response.data.models;
                    if (this.models.length > 0) {
                        // Use the default model from the API response (from centralized config)
                        this.selectedModel = response.data.default_model;

                        // If default model is not in the list, fall back to first model
                        if (!this.models.find(m => m.id === this.selectedModel)) {
                            this.selectedModel = this.models[0].id;
                        }

                        // Automatically set the LLM when provider changes
                        await this.setLLM();
                    }
                } catch (error) {
                    console.error('Error fetching models:', error);
                    this.models = [];
                }
            },

            async setLLM() {
                this.isSettingLLM = true;
                try {
                    // Set the MCP protocol based on the selected provider
                    if (this.selectedProvider === 'mcp') {
                        this.mcpProtocol = 'SSE';
                    } else if (this.selectedProvider === 'mcp-http') {
                        this.mcpProtocol = 'HTTP';
                    } else {
                        this.mcpProtocol = '';
                    }

                    await axios.post('/gaia_chat/api/llm/set/', {
                        provider: this.selectedProvider,
                        model: this.selectedModel
                    });
                } catch (error) {
                    console.error('Error setting LLM:', error);
                } finally {
                    this.isSettingLLM = false;
                }
            },

            async fetchConversations() {
                try {
                    const response = await axios.get('/gaia_chat/api/conversations/');
                    this.conversations = response.data.conversations;
                } catch (error) {
                    console.error('Error fetching conversations:', error);
                }
            },

            async createConversation() {
                this.isCreatingConversation = true;
                try {
                    console.log('Creating new conversation...');

                    // Use the custom title if provided, otherwise use a default title
                    const title = this.newConversationTitle.trim()
                        ? this.newConversationTitle.trim()
                        : `Conversation ${new Date().toLocaleString()}`;

                    console.log('Using title:', title);

                    // Prepare the request payload
                    const payload = { title: title };

                    // If we have context data, include it in the request
                    if (this.chatContext && this.chatContext.data && this.chatContext.data.length) {
                        console.log('Including context data in conversation creation...');
                        payload.context = this.chatContext;
                    }

                    const response = await axios.post('/gaia_chat/api/conversations/create/', payload);

                    console.log('Create conversation response:', response.data);

                    if (response.data.success) {
                        // Clear the title input
                        this.newConversationTitle = '';

                        // Refresh the conversation list
                        await this.fetchConversations();

                        // Get the newly created conversation ID
                        const newConversationId = response.data.conversation.id;
                        console.log('New conversation ID:', newConversationId);

                        // Load the new conversation (context will already be loaded by backend)
                        await this.loadConversation(newConversationId);
                        console.log('Loaded conversation:', this.activeConversation);
                    }
                } catch (error) {
                    console.error('Error creating conversation:', error);
                    if (error.response) {
                        console.error('Response status:', error.response.status);
                        console.error('Response data:', error.response.data);
                    }
                    this.error = "Error creating conversation: " + (error.response?.data?.error || error.message);
                } finally {
                    this.isCreatingConversation = false;
                }
            },

            async loadConversation(id) {
                this.isLoadingConversation = true;
                try {
                    const response = await axios.get(`/gaia_chat/api/conversations/${id}/`);
                    if (response.data.success) {
                        this.activeConversation = response.data.conversation;

                        // Make sure the conversation has a messages array
                        if (!this.activeConversation.messages) {
                            this.activeConversation.messages = [];
                        }

                        // Update the URL to include the conversation ID while preserving the current path
                        const currentPath = window.location.pathname;
                        const newUrl = `${currentPath}?conversation=${id}`;
                        window.history.pushState({}, '', newUrl);

                        this.$nextTick(() => {
                            this.scrollToBottom();
                        });
                    }
                } catch (error) {
                    console.error('Error loading conversation:', error);
                    this.error = "Error loading conversation: " + (error.response?.data?.error || error.message);
                } finally {
                    this.isLoadingConversation = false;
                }
            },

            isDirectToolCall(message) {
                // Check if the message is a direct tool call (simple tool name)
                const trimmed = message.trim();

                // List of known direct tool calls
                const directToolCalls = [
                    'long_task',
                    'echostring_table',
                    'firecrawl_scrape',
                    'firecrawl_scrape_text_only'
                ];

                return directToolCalls.includes(trimmed);
            },

            async sendMessage() {
                if (!this.userInput.trim()) return;

                const message = this.userInput;
                this.userInput = '';

                // If there's no active conversation, create one automatically
                if (!this.activeConversation) {
                    console.log('No active conversation, creating one automatically...');
                    try {
                        await this.createConversation();
                        // createConversation will set activeConversation, so we can proceed
                    } catch (error) {
                        console.error('Failed to create conversation automatically:', error);
                        this.error = "Failed to create conversation. Please try creating one manually.";
                        return;
                    }
                }

                // Check if this is a direct tool call for MCP providers
                const isDirectToolCall = this.isDirectToolCall(message);
                const isMcpProvider = this.selectedProvider === 'mcp' || this.selectedProvider === 'mcp-http';

                console.log(`🔍 PROVIDER CHECK: selectedProvider = "${this.selectedProvider}"`);
                console.log(`🔧 TOOL CHECK: isDirectToolCall = ${isDirectToolCall}`);

                if (isMcpProvider && isDirectToolCall) {
                    console.log('🚀 USING STREAMING ENDPOINT for MCP provider with direct tool call');
                    await this.sendStreamingMessage(message);
                } else {
                    console.log('📝 USING REGULAR ENDPOINT for regular message or non-MCP provider');
                    await this.sendRegularMessage(message);
                }
            },

            async sendRegularMessage(message) {
                this.isSendingMessage = true;

                try {
                    // Add the user message to the conversation
                    this.activeConversation.messages.push({
                        role: 'user',
                        content: message,
                        timestamp: new Date().toISOString()
                    });

                    // Prepare the messages to send to the API
                    let messagesToSend = [...this.activeConversation.messages];

                    // Send the messages to the API
                    const response = await axios.post('/gaia_chat/api/chat/', {
                        conversation_id: this.activeConversation.id,
                        messages: messagesToSend,
                        provider: this.selectedProvider
                    });

                    // Add the assistant's response to the conversation
                    this.activeConversation.messages.push({
                        role: 'assistant',
                        content: response.data.message,
                        timestamp: new Date().toISOString()
                    });

                    // Save the conversation using the new method
                    await this.updateConversation(this.activeConversation.id, this.activeConversation.messages);

                    this.scrollToBottom();
                } catch (error) {
                    console.error('Error sending message:', error);
                    this.error = "Error sending message: " + (error.response?.data?.error || error.message);
                } finally {
                    this.isSendingMessage = false;
                }
            },

            async sendStreamingMessage(message) {
                this.isStreamingMessage = true;

                // Reset streaming state
                this.streamingProgress = [];
                this.streamingInfo = [];
                this.toolResult = null;

                try {
                    // Add the user message to the conversation
                    this.activeConversation.messages.push({
                        role: 'user',
                        content: message,
                        timestamp: new Date().toISOString()
                    });

                    // Add a placeholder for the assistant's response
                    this.activeConversation.messages.push({
                        role: 'assistant',
                        content: '',
                        timestamp: new Date().toISOString(),
                        streaming: true
                    });

                    const assistantMessageIndex = this.activeConversation.messages.length - 1;

                    // Use the send_message_stream endpoint for real streaming
                    const response = await fetch('/gaia_chat/api/messages/send-stream/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': this.getCsrfToken()
                        },
                        body: JSON.stringify({
                            message: message,
                            conversation_id: this.activeConversation.id,
                            provider: this.selectedProvider
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));

                                    // Handle all streaming events through the unified handler
                                    this.handleStreamingData(data, assistantMessageIndex);

                                    // Handle completion events
                                    if (data.type === 'complete' || data.type === 'final') {
                                        this.isStreamingMessage = false;
                                        this.activeConversation.messages[assistantMessageIndex].streaming = false;

                                        // Save the conversation
                                        await this.updateConversation(this.activeConversation.id, this.activeConversation.messages);
                                        return;
                                    }
                                } catch (e) {
                                    console.error('Error parsing streaming data:', e, line);
                                }
                            }
                        }
                    }

                } catch (error) {
                    console.error('Error sending streaming message:', error);
                    this.error = "Error sending streaming message: " + error.message;
                    this.isStreamingMessage = false;
                    if (this.activeConversation.messages.length > 0) {
                        this.activeConversation.messages[this.activeConversation.messages.length - 1].streaming = false;
                    }
                }
            },

            async handleStreamingData(data, assistantMessageIndex) {
                // 🎯 CONSOLE LOGGING: Log all streaming data to browser console
                console.log('📊 STREAMING DATA:', data.type, data);

                switch (data.type) {
                    case 'start':
                        console.log('🚀 TASK STARTED:', data.content);
                        this.activeConversation.messages[assistantMessageIndex].content = '🚀 ' + data.content;
                        break;

                    case 'progress':
                        // Add progress update to the list
                        this.streamingProgress.push(data);

                        // 🎯 CONSOLE LOGGING: Use the exact console message from backend (same format as chat_term.py)
                        if (data.console_message) {
                            console.log(data.console_message);
                        } else {
                            // Fallback to building the message (for backward compatibility)
                            let progressText;
                            if (data.total && data.bar && data.percentage !== null) {
                                // Progress with visual bar
                                progressText = `📊 [${data.bar}] ${data.percentage.toFixed(1)}% ${data.progress}/${data.total}`;
                            } else {
                                // Progress without total (step-based)
                                progressText = `📊 Step ${data.progress}`;
                            }
                            const progressMessage = data.message ? ` – ${data.message}` : '';
                            console.log(progressText + progressMessage);
                        }

                        // Build content with all progress updates
                        let content = '🚀 Task in progress...\n\n';
                        this.streamingProgress.forEach(p => {
                            let pText;
                            if (p.total && p.bar && p.percentage !== null) {
                                pText = `📊 [${p.bar}] ${p.percentage.toFixed(1)}% ${p.progress}/${p.total}`;
                            } else {
                                pText = `📊 Step ${p.progress}`;
                            }
                            const pMessage = p.message ? ` – ${p.message}` : '';
                            content += pText + pMessage + '\n';
                        });

                        this.activeConversation.messages[assistantMessageIndex].content = content;
                        break;

                    case 'info':
                        // Add info message to the list
                        this.streamingInfo.push(data);

                        // Update the current message to show info
                        let currentContent = this.activeConversation.messages[assistantMessageIndex].content;
                        if (!currentContent.includes(data.content)) {
                            currentContent += '\n' + data.content;
                            this.activeConversation.messages[assistantMessageIndex].content = currentContent;
                        }
                        break;

                    case 'log':
                        // Handle log messages from FastMCP
                        this.streamingInfo.push(data);

                        // Display log message with emoji
                        const logMessage = `${data.emoji} [${data.level}] ${data.logger}: ${data.message}`;
                        let logContent = this.activeConversation.messages[assistantMessageIndex].content;
                        logContent += '\n' + logMessage;
                        this.activeConversation.messages[assistantMessageIndex].content = logContent;
                        break;

                    case 'summary':
                        // Handle progress summary from FastMCP
                        const summaryMessage = `\n${data.content}\n📊 Progress msgs: ${data.progress_count}   ℹ️ Info msgs: ${data.info_count}`;
                        let summaryContent = this.activeConversation.messages[assistantMessageIndex].content;
                        summaryContent += summaryMessage;
                        this.activeConversation.messages[assistantMessageIndex].content = summaryContent;
                        break;

                    case 'result':
                        // Handle tool result from FastMCP - store for final processing
                        this.toolResult = data.content;
                        break;

                    case 'final':
                        // Update with final result
                        let finalContent = '';

                        // Add progress summary if any
                        if (this.streamingProgress.length > 0) {
                            finalContent += '✅ Task completed successfully!\n\n';
                            finalContent += `📊 Progress updates: ${this.streamingProgress.length}\n`;
                            finalContent += `ℹ️ Info messages: ${this.streamingInfo.length}\n\n`;
                        }

                        finalContent += data.content;

                        this.activeConversation.messages[assistantMessageIndex].content = finalContent;
                        this.activeConversation.messages[assistantMessageIndex].streaming = false;
                        break;

                    case 'complete':
                        // Task completed
                        this.activeConversation.messages[assistantMessageIndex].streaming = false;
                        break;

                    case 'error':
                        this.activeConversation.messages[assistantMessageIndex].content = `❌ Error: ${data.content}`;
                        this.activeConversation.messages[assistantMessageIndex].streaming = false;
                        this.error = data.content;
                        break;
                }

                this.scrollToBottom();
            },

            // Utility methods
            getCsrfToken() {
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'csrftoken') {
                        return value;
                    }
                }
                return '';
            },

            formatDate(dateString) {
                if (!dateString) return '';
                const date = new Date(dateString);
                return date.toLocaleString();
            },

            formatRole(role) {
                if (role === 'user') return this.djangoData.username || 'You';
                if (role === 'assistant') return 'AgFunder Gaia';
                if (role === 'system') return 'AgFunder Gaia';
                return role;
            },


            /* public formatter you call when displaying a message */
            formatMessage (content) {
                console.log('///////////////////////// Formatting message using the Composite pattern....')
                console.log('🔍 Content preview:', content.substring(0, 200));
                const json = this.extractJsonBlock(content);

                /* 1️⃣ JSON-described composites */
                if (json) {
                    console.log('✅ Found JSON block for composite rendering:', json);
                    const node      = this.buildNode(json);
                    const rendered  = node.render();
                    const remaining = content.replace(json.__raw, '').trim();
                    return rendered + (remaining ? this.plainTextToHtml(remaining) : '');
                }

                /* 2️⃣ tool-call results (original behaviour) */
                if (this.hasToolCallResults(content)) {
                    console.log('✅ Found tool call results, using formatToolCallResults');
                    return this.formatToolCallResults(content);
                }

                /* 3️⃣ fallback: markdown rendering */
                console.log('⚠️ Using fallback markdown rendering');
                return marked.parse(content);
            },

            /* ---------- helper functions now methods ---------- */
            escapeHtml (str)       { return ChatHelpers.escapeHtml(str); },
            plainTextToHtml (txt)  { return ChatHelpers.plainTextToHtml(txt); },

            extractJsonBlock (text) {
                console.log('🔍 extractJsonBlock called with text length:', text.length);

                // First try to match objects with "type" property (tables, lists, etc.)
                let match = text.match(/\{[^]*?"type"\s*:\s*"[a-z]+"[^]*?\}/);
                if (match) {
                    console.log('✅ Found type-based JSON match 1', match);
                }

                // If no type-based match, try to match SVG content objects
                if (!match) {
                    match = text.match(/\{[^]*?"svg_content"\s*:[^]*?"success"\s*:[^]*?\}/) ||
                            text.match(/\{[^]*?"success"\s*:[^]*?"svg_content"\s*:[^]*?\}/) ||
                            text.match(/\{[^]*?"chart_type"\s*:[^]*?"svg_content"\s*:[^]*?\}/);
                    if (match) {
                        console.log('✅ Found SVG content JSON match 2', match);
                    }
                }

                // If still no match, try to extract from text='...' format (tool results)
                if (!match) {
                    console.log('🔍 Trying text=... format extraction');
                    const textMatch = text.match(/text='(\{.*?\})'/s) || text.match(/text="(\{.*?\})"/s);
                    if (textMatch) {
                        console.log('✅ Found text=... match, attempting to parse JSON');
                        try {
                            // Try to parse the extracted JSON to see if it's SVG content
                            const jsonText = textMatch[1]
                                .replace(/\\n/g, '\n')
                                .replace(/\\"/g, '"')
                                .replace(/\\'/g, "'")
                                .replace(/\\\\/g, '\\');
                            const parsed = JSON.parse(jsonText);
                            if (parsed.svg_content && (parsed.success || parsed.chart_type)) {
                                console.log('🎨 Found SVG content in text=... format!');
                                parsed.__raw = textMatch[0];
                                return parsed;
                            } else {
                                console.log('❌ JSON parsed but no SVG content found');
                            }
                        } catch (e) {
                            console.log('❌ Failed to parse JSON from text=... format:', e.message);
                        }
                    } else {
                        console.log('❌ No text=... pattern found');
                    }
                }

                if (!match) {
                    console.log('❌ No JSON block found');
                    return null;
                }
                try {
                    console.log('🧹 Cleaning matched JSON before parsing');
                    console.log('📝 Raw matched text (first 200 chars):', match[0].substring(0, 200));

                    // Try a completely different approach: extract SVG content directly without JSON parsing
                    if (match[0].includes('"svg_content"') && match[0].includes('"success"')) {
                        console.log('🎨 Detected SVG content, trying direct extraction...');

                        // Extract the SVG content directly using a more robust regex
                        // The SVG content is between "svg_content": " and the next unescaped "
                        const svgStart = match[0].indexOf('"svg_content"');
                        if (svgStart !== -1) {
                            const contentStart = match[0].indexOf('"', svgStart + '"svg_content"'.length + 1) + 1;
                            let contentEnd = contentStart;
                            let escapeNext = false;

                            // Find the end of the SVG content string, handling escaped quotes
                            for (let i = contentStart; i < match[0].length; i++) {
                                if (escapeNext) {
                                    escapeNext = false;
                                    continue;
                                }
                                if (match[0][i] === '\\') {
                                    escapeNext = true;
                                    continue;
                                }
                                if (match[0][i] === '"') {
                                    contentEnd = i;
                                    break;
                                }
                            }

                            let svgContent = match[0].substring(contentStart, contentEnd);
                            console.log('✅ Extracted SVG content directly, length:', svgContent.length);
                            console.log('🔍 SVG content preview (first 100 chars):', svgContent.substring(0, 100));

                            // The content is JSON-escaped, so let's properly parse it
                            console.log('🔧 Starting unescaping process...');
                            console.log('🔧 Original length:', svgContent.length);

                            try {
                                // Try to parse as JSON string first
                                svgContent = JSON.parse('"' + svgContent + '"');
                                console.log('🔧 After JSON parsing, length:', svgContent.length);
                                console.log('🔧 JSON parsing successful!');
                            } catch (e) {
                                console.log('🔧 JSON parsing failed, using manual unescaping');
                                // Fallback to manual unescaping
                                svgContent = svgContent.replace(/\\n/g, '\n');
                                svgContent = svgContent.replace(/\\r/g, '\r');
                                svgContent = svgContent.replace(/\\t/g, '\t');
                                svgContent = svgContent.replace(/\\"/g, '"');
                                svgContent = svgContent.replace(/\\'/g, "'");
                                svgContent = svgContent.replace(/\\\\/g, '\\');
                                console.log('🔧 After manual unescaping, length:', svgContent.length);
                            }



                            console.log('🔍 After unescaping, length:', svgContent.length);
                            console.log('🔍 After unescaping preview (first 100 chars):', svgContent.substring(0, 100));

                            // Return a properly formatted object
                            const obj = {
                                success: true,
                                svg_content: svgContent,
                                chart_type: 'Chart',
                                __raw: match[0]
                            };
                            console.log('✅ Successfully extracted SVG content directly');
                            return obj;
                        }
                    }

                    // Fallback to original JSON parsing approach
                    let cleanedJson = match[0];

                    // Simple cleaning approach - just remove problematic line continuation backslashes
                    cleanedJson = cleanedJson
                        // Remove line continuation backslashes that break JSON
                        .replace(/\\\s*\n/g, '\\n')
                        .replace(/\\\s*\r\n/g, '\\n')
                        .replace(/\\\s*\r/g, '\\n')
                        // Standard JSON unescaping
                        .replace(/\\n/g, '\n')
                        .replace(/\\r/g, '\r')
                        .replace(/\\t/g, '\t')
                        .replace(/\\"/g, '"')
                        .replace(/\\'/g, "'")
                        .replace(/\\\\/g, '\\');

                    console.log('🧹 Cleaned JSON text (first 200 chars):', cleanedJson.substring(0, 200));

                    const obj = JSON.parse(cleanedJson.trim());
                    obj.__raw = match[0];
                    console.log('✅ Successfully parsed JSON block');
                return obj;
                } catch (e) {
                    console.log('❌ Failed to parse matched JSON', e);
                    console.log('❌ Error at position:', e.message.match(/position (\d+)/)?.[1] || 'unknown');
                    return null;
                }
            },

            buildNode (data) {
                if (typeof data === 'string') return new TextNode(data);
                if (typeof data === 'number') return new TextNode(data.toString());
                if (typeof data === 'boolean') return new TextNode(data.toString());
                if (data === null || data === undefined) return new TextNode('');

                // Handle objects
                if (typeof data === 'object') {
                    // Check for SVG content first (before type checking)
                    if (data.svg_content && (data.success || data.chart_type)) {
                        return new SVGNode(data);
                    }

                    // Check if it has a type property for structured data
                    if (data.type) {
                        switch (data.type) {
                        case 'list': {
                            const list = new ListNode();
                            data.items.forEach(item => list.add(this.buildNode(item)));
                            return list;
                        }
                        case 'table': {
                            const tbl = new TableNode(data);
                            data.rows.forEach(r => tbl.addRow(...r.map(cell => this.buildNode(cell))));
                            return tbl;
                        }
                        case 'svg': {
                            return new SVGNode(data);
                        }
                        default:
                            throw new Error(`Unknown node type: ${data.type}`);
                        }
                    } else {
                        // Handle plain objects by converting to JSON string
                        return new TextNode(JSON.stringify(data, null, 2));
                    }
                }

                // Fallback for any other data types
                return new TextNode(String(data));
            },

            

            /*
            formatMessage(content) {
                // Check if the content contains table data in JSON format
                if (this.isTableData(content)) {
                    return this.formatTableData(content);
                }

                // Check if the content contains tool call results and format them
                if (this.hasToolCallResults(content)) {
                    return this.formatToolCallResults(content);
                }

                // Use marked to render markdown for regular content
                return marked.parse(content);
            },

            isTableData(content) {
                // Check if content contains JSON table data
                try {
                    // Look for table data patterns in the content
                    const jsonMatch = content.match(/\{[^}]*"type"\s*:\s*"table"[^}]*\}/);
                    if (jsonMatch) {
                        const tableData = JSON.parse(jsonMatch[0]);
                        return tableData.type === 'table' && tableData.columns && tableData.rows;
                    }
                    return false;
                } catch (e) {
                    return false;
                }
            },

            formatTableData(content) {
                try {
                    // Extract JSON table data from the content
                    const jsonMatch = content.match(/\{[^}]*"type"\s*:\s*"table"[^}]*\}/);
                    if (!jsonMatch) {
                        return marked.parse(content);
                    }

                    const tableData = JSON.parse(jsonMatch[0]);

                    // Build HTML table
                    let html = '';

                    // Add title if present
                    if (tableData.title) {
                        html += `<h4 style="margin-bottom: 10px; color: #333;">${tableData.title}</h4>`;
                    }

                    html += '<table style="border-collapse: collapse; width: 100%; margin: 10px 0; border: 1px solid #ddd;">';

                    // Add headers
                    if (tableData.columns && tableData.columns.length > 0) {
                        html += '<thead><tr>';
                        tableData.columns.forEach(column => {
                            html += `<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; text-align: left; font-weight: bold;">${column}</th>`;
                        });
                        html += '</tr></thead>';
                    }

                    // Add rows
                    if (tableData.rows && tableData.rows.length > 0) {
                        html += '<tbody>';
                        tableData.rows.forEach((row, rowIndex) => {
                            const bgColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';
                            html += `<tr style="background-color: ${bgColor};">`;
                            row.forEach(cell => {
                                html += `<td style="border: 1px solid #ddd; padding: 8px;">${cell}</td>`;
                            });
                            html += '</tr>';
                        });
                        html += '</tbody>';
                    }

                    html += '</table>';

                    // Process the rest of the content (remove the JSON part and render as markdown)
                    const remainingContent = content.replace(jsonMatch[0], '').trim();
                    if (remainingContent) {
                        html += marked.parse(remainingContent);
                    }

                    return html;
                } catch (e) {
                    console.error('Error formatting table data:', e);
                    // Fallback to regular markdown rendering
                    return marked.parse(content);
                }
            },
            */

            

            hasToolCallResults(content) {
                // Check if content contains tool call result patterns
                // Based on console logs: Result: [TextContent(type='text', text='{"columns": [...], "data": [...]}', annotations=None)]
                const patterns = [
                    /Result:\s*\[TextContent\(/,
                    /TextContent\(type='text'/,
                ];

                const hasResults = patterns.some(pattern => pattern.test(content));
                if (hasResults) {
                    console.log('✅ hasToolCallResults: TRUE for content:', content.substring(0, 300));
                } else {
                    console.log('❌ hasToolCallResults: FALSE for content:', content.substring(0, 100));
                }
                return hasResults;
            },

            formatToolCallResults(content) {
                try {
                    // Split content into parts and identify tool call results
                    const parts = [];
                    let currentIndex = 0;

                    // Robust regex to match tool call results: "Result: [TextContent(...)]" with proper bracket matching
                    // Updated to handle the actual format seen in screenshots
                    const toolResultRegex = /Result:\s*\[(.*?)annotations=None\)\]/g;
                    let match;

                    while ((match = toolResultRegex.exec(content)) !== null) {
                        // Add content before the tool result
                        if (match.index > currentIndex) {
                            const beforeContent = content.substring(currentIndex, match.index).trim();
                            if (beforeContent) {
                                parts.push({
                                    type: 'text',
                                    content: beforeContent
                                });
                            }
                        }

                        // Remove the Surrounding ToolCall text
                        let toolResultContent = match[0]
                        let startText = "Result: [TextContent(type='text',"
                        let endText = ", annotations=None)]"
                        toolResultContent = toolResultContent.replace(startText, '').replace(endText, '');
                        // Add the tool result
                        parts.push({
                            type: 'tool_result',
                            content: toolResultContent
                        });

                        currentIndex = match.index + match[0].length;
                    }

                    // Add remaining content after the last tool result
                    if (currentIndex < content.length) {
                        const remainingContent = content.substring(currentIndex).trim();
                        if (remainingContent) {
                            parts.push({
                                type: 'text',
                                content: remainingContent
                            });
                        }
                    }

                    

                    // Build HTML with tool results formatted as gray code boxes
                    let html = '';
                    parts.forEach(part => {
                        if (part.type === 'tool_result') {
                            // Try to format JSON content within the tool result
                            let formattedContent = this.formatToolResultContent(part.content);

                            console.log('formatToolResultContent:', formattedContent);

                            // Format as gray code box
                            html += `<div class="tool-call-result">
                                <div class="tool-call-header">Tool Call Result</div>
                                <div class="tool-call-content">${formattedContent}</div>
                            </div>`;
                        } else {
                            // Regular markdown content
                            html += marked.parse(part.content);
                        }
                    });

                    return html;
                } catch (e) {
                    console.error('Error formatting tool call results:', e);
                    // Fallback to regular markdown rendering
                    return marked.parse(content);
                }
            },

            formatToolResultContent(content) {
                try {
                    console.log('🔧 formatToolResultContent called with content length:', content.length);
                    //console.log('🔧 Content sample:', content.substring(0, 500));

                    // Look for JSON content in the tool result
                    // Based on logs: Result: [TextContent(type='text', text='{"columns": [...], "data": [...]}', annotations=None)]

                    // Try to find JSON content between text=' and ' - handle the exact format from logs
                    let jsonMatch = content.match(/text='(\{[^}]*"columns"[^}]*"data"[^}]*\})'/);

                    if (!jsonMatch) {
                        // Try with double quotes
                        jsonMatch = content.match(/text="(\{[^}]*"columns"[^}]*"data"[^}]*\})"/);
                    }

                    if (!jsonMatch) {
                        // Try broader pattern for any JSON-like content in text field
                        jsonMatch = content.match(/text='(\{.*?\})'/s);
                    }

                    if (!jsonMatch) {
                        // Try with double quotes version
                        jsonMatch = content.match(/text="(\{.*?\})"/s);
                    }

                    // Check for SVG content first (before JSON processing)
                    // Use a more robust pattern that captures the complete SVG content
                    const svgMatch = content.match(/text='(<svg[^>]*>[\s\S]*?<\/svg>)'/s) ||
                                   content.match(/text="(<svg[^>]*>[\s\S]*?<\/svg>)"/s) ||
                                   content.match(/text='(<svg[\s\S]*?<\/svg>)'/s) ||
                                   content.match(/text="(<svg[\s\S]*?<\/svg>)"/s);

                    if (svgMatch) {
                        let svgContent = svgMatch[1];
                        console.log('🎨 Detected SVG content, length:', svgContent.length);
                        console.log('Raw SVG content (first 200 chars):', svgContent.substring(0, 200));

                        // Unescape the SVG content (both JavaScript and HTML entities)
                        svgContent = svgContent
                            .replace(/\\n/g, '\n')
                            .replace(/\\"/g, '"')
                            .replace(/\\'/g, "'")
                            .replace(/\\\\/g, '\\')
                            .replace(/&lt;/g, '<')
                            .replace(/&gt;/g, '>')
                            .replace(/&quot;/g, '"')
                            .replace(/&#x27;/g, "'")
                            .replace(/&amp;/g, '&');

                        console.log('Unescaped SVG content (first 200 chars):', svgContent.substring(0, 200));

                        // Create a container for the SVG with proper styling
                        const svgContainer = `
                            <div class="svg-content" style="
                                margin: 10px 0;
                                padding: 15px;
                                border: 1px solid #e0e0e0;
                                border-radius: 8px;
                                background: #fafafa;
                                text-align: center;
                                overflow: auto;
                            ">
                                <div class="svg-title" style="
                                    font-size: 14px;
                                    color: #666;
                                    margin-bottom: 10px;
                                    font-weight: bold;
                                ">SVG Chart</div>
                                <div class="svg-wrapper" style="
                                    max-width: 100%;
                                    overflow: auto;
                                ">${svgContent}</div>
                            </div>`;

                        // Replace the SVG part with the rendered container
                        const beforeMatch = content.substring(0, content.indexOf(svgMatch[0]));
                        const afterMatch = content.substring(content.indexOf(svgMatch[0]) + svgMatch[0].length);


                        console.log('svgContainer:', svgContainer);
                        console.log('content before match:', beforeMatch);
                        console.log('content after match:', afterMatch);

                        return `<pre>${this.escapeHtml(beforeMatch)}</pre>${svgContainer}<pre>${this.escapeHtml(afterMatch)}</pre>`;
                    }

                    if (jsonMatch) {
                        let jsonText = jsonMatch[1];
                        console.log('📝 Raw extracted text (first 300 chars):', jsonText.substring(0, 300));

                        // Check if this might be SVG chart data before general JSON processing
                        if (jsonText.includes('"svg_content"') && (jsonText.includes('"success"') || jsonText.includes('"chart_type"'))) {
                            console.log('🎨 Detected potential SVG chart data in JSON, attempting to parse...');

                            // Use enhanced cleaning logic for SVG content
                            let cleanedJson = jsonText;

                            // Fix line continuation backslashes in SVG content that break JSON parsing
                            cleanedJson = cleanedJson
                                // Convert line continuation backslashes to proper JSON newlines
                                .replace(/\\\s*\n/g, '\\n')  // Convert backslash+newline to proper JSON newline
                                .replace(/\\\s*\r\n/g, '\\n') // Convert backslash+CRLF to proper JSON newline
                                .replace(/\\\s*\r/g, '\\n')   // Convert backslash+CR to proper JSON newline
                                // Handle specific SVG patterns
                                .replace(/\?>\\\s*<!DOCTYPE/g, '?><!DOCTYPE')
                                .replace(/dtd\\">\\\s*</g, 'dtd\\">\\n<')
                                // Remove line continuation backslashes at actual end of lines
                                .replace(/\\$/gm, '')
                                // Handle double-escaped quotes
                                .replace(/\\\\"/g, '\\"')
                                // Standard JSON unescaping (do this last)
                                .replace(/\\n/g, '\n')
                                .replace(/\\r/g, '\r')
                                .replace(/\\t/g, '\t')
                                .replace(/\\"/g, '"')
                                .replace(/\\'/g, "'")
                                .replace(/\\\\/g, '\\');

                            console.log('🧹 SVG JSON cleaned text (first 200 chars):', cleanedJson.substring(0, 200));

                            try {
                                const parsed = JSON.parse(cleanedJson.trim());
                                if (parsed.svg_content && (parsed.success !== false)) {
                                    console.log('✅ Successfully parsed SVG chart JSON, rendering SVG');
                                    const svgContent = parsed.svg_content;
                                    const chartType = parsed.chart_type || 'Chart';

                                    // Create a container for the SVG with proper styling
                                    const svgContainer = `
                                        <div class="svg-chart-content" style="
                                            margin: 15px 0;
                                            padding: 20px;
                                            border: 1px solid #e0e0e0;
                                            border-radius: 8px;
                                            background: #fafafa;
                                            text-align: center;
                                            overflow: auto;
                                        ">
                                            <div class="svg-title" style="
                                                font-size: 16px;
                                                color: #333;
                                                margin-bottom: 15px;
                                                font-weight: bold;
                                            ">${chartType.replace('_', ' ').toUpperCase()}</div>
                                            <div class="svg-wrapper" style="
                                                max-width: 100%;
                                                overflow: auto;
                                                background: white;
                                                border-radius: 4px;
                                                padding: 10px;
                                            ">${svgContent}</div>
                                        </div>`;

                                    // Return just the SVG container without the surrounding pre tags
                                    return svgContainer;
                                }
                            } catch (e) {
                                console.log('❌ Failed to parse SVG chart JSON:', e.message);
                                console.log('❌ SVG JSON error at position:', e.message.match(/position (\d+)/)?.[1] || 'unknown');
                                const errorPos = parseInt(e.message.match(/position (\d+)/)?.[1] || '100');
                                console.log('❌ SVG JSON problematic text around error:',
                                    cleanedJson.substring(Math.max(0, errorPos - 50), errorPos + 50));

                                // Try a more aggressive cleaning approach for SVG content
                                try {
                                    console.log('🔧 Attempting aggressive SVG JSON cleaning...');
                                    let aggressiveClean = jsonText
                                        // More aggressive approach: convert all line continuation patterns to proper JSON
                                        .replace(/\\\s*\n/g, '\\n')  // Convert backslash+newline to proper JSON newline
                                        .replace(/\\\s*\r\n/g, '\\n') // Convert backslash+CRLF to proper JSON newline
                                        .replace(/\\\s*\r/g, '\\n')   // Convert backslash+CR to proper JSON newline
                                        // Remove backslashes before XML angle brackets (these are definitely wrong)
                                        .replace(/\\(?=\s*[<>])/g, '')
                                        // Handle specific SVG patterns more aggressively
                                        .replace(/\?>\\\s*<!DOCTYPE/g, '?><!DOCTYPE')
                                        .replace(/dtd\\">\\\s*</g, 'dtd\\">\\n<')
                                        // Remove any remaining problematic backslashes at end of lines
                                        .replace(/\\$/gm, '')
                                        // Standard unescaping
                                        .replace(/\\"/g, '"')
                                        .replace(/\\'/g, "'")
                                        .replace(/\\n/g, '\n')
                                        .replace(/\\r/g, '\r')
                                        .replace(/\\t/g, '\t')
                                        .replace(/\\\\/g, '\\');

                                    console.log('🧹 Aggressively cleaned SVG JSON (first 200 chars):', aggressiveClean.substring(0, 200));
                                    const parsed = JSON.parse(aggressiveClean.trim());

                                    if (parsed.svg_content && (parsed.success !== false)) {
                                        console.log('✅ Successfully parsed SVG chart JSON with aggressive cleaning');
                                        const svgContent = parsed.svg_content;
                                        const chartType = parsed.chart_type || 'Chart';

                                        const svgContainer = `
                                            <div class="svg-chart-content" style="
                                                margin: 15px 0;
                                                padding: 20px;
                                                border: 1px solid #e0e0e0;
                                                border-radius: 8px;
                                                background: #fafafa;
                                                text-align: center;
                                                overflow: auto;
                                            ">
                                                <div class="svg-title" style="
                                                    font-size: 16px;
                                                    color: #333;
                                                    margin-bottom: 15px;
                                                    font-weight: bold;
                                                ">${chartType.replace('_', ' ').toUpperCase()}</div>
                                                <div class="svg-wrapper" style="
                                                    max-width: 100%;
                                                    overflow: auto;
                                                    background: white;
                                                    border-radius: 4px;
                                                    padding: 10px;
                                                ">${svgContent}</div>
                                            </div>`;
                                        return svgContainer;
                                    }
                                } catch (e2) {
                                    console.log('❌ Aggressive SVG JSON cleaning also failed:', e2.message);
                                }
                                // Fall through to regular JSON processing
                            }
                        }

                        // Unescape the content and fix malformed JSON
                        jsonText = jsonText
                            // Fix line continuation backslashes that break JSON parsing
                            .replace(/\\\s*\n/g, '\\n')  // Convert backslash+newline to proper JSON newline
                            .replace(/\\\s*\r\n/g, '\\n') // Convert backslash+CRLF to proper JSON newline
                            .replace(/\\\s*\r/g, '\\n')   // Convert backslash+CR to proper JSON newline
                            // Handle specific SVG patterns
                            .replace(/\?>\\\s*<!DOCTYPE/g, '?><!DOCTYPE')
                            .replace(/dtd\\">\\\s*</g, 'dtd\\">\\n<')
                            // Remove line continuation backslashes at actual end of lines
                            .replace(/\\$/gm, '')
                            // Handle double-escaped quotes
                            .replace(/\\\\"/g, '\\"')
                            // Standard JSON unescaping (do this last)
                            .replace(/\\n/g, '\n')
                            .replace(/\\r/g, '\r')
                            .replace(/\\t/g, '\t')
                            .replace(/\\"/g, '"')
                            .replace(/\\'/g, "'")
                            .replace(/\\\\/g, '\\');

                        console.log('🧹 Cleaned text (first 300 chars):', jsonText.substring(0, 300));

                        // Check if it looks like JSON
                        const trimmed = jsonText.trim();
                        if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
                            try {
                                const parsed = JSON.parse(trimmed);
                                console.log('✅ Successfully parsed JSON with keys:', Object.keys(parsed));

                                // Check if this is a table data structure
                                if (parsed.type === 'table') {
                                    console.log('🔧 Detected table data structure, using Composite pattern');
                                    const node = this.buildNode(parsed);
                                    const tableHtml = node.render();

                                    // Replace the JSON part with table
                                    const beforeMatch = content.substring(0, content.indexOf(jsonMatch[0]));
                                    const afterMatch = content.substring(content.indexOf(jsonMatch[0]) + jsonMatch[0].length);

                                    return `<pre>${this.escapeHtml(beforeMatch)}</pre><div class="table-content">${tableHtml}</div><pre>${this.escapeHtml(afterMatch)}</pre>`;
                                } else {
                                    // Regular JSON highlighting
                                    const prettyJson = JSON.stringify(parsed, null, 4);
                                    const highlightedJson = this.highlightJson(prettyJson);

                                    // Replace the JSON part with highlighted version
                                    const beforeMatch = content.substring(0, content.indexOf(jsonMatch[0]));
                                    const afterMatch = content.substring(content.indexOf(jsonMatch[0]) + jsonMatch[0].length);

                                    return `<pre>${this.escapeHtml(beforeMatch)}</pre><div class="json-content">${highlightedJson}</div><pre>${this.escapeHtml(afterMatch)}</pre>`;
                                }
                            } catch (parseError) {
                                console.log('❌ JSON parse error:', parseError.message);
                                console.log('❌ Failed text sample:', trimmed.substring(0, 100));

                                // Try to find a valid JSON substring
                                const jsonStart = trimmed.indexOf('{');
                                const jsonArrayStart = trimmed.indexOf('[');
                                let startIndex = -1;

                                if (jsonStart !== -1 && (jsonArrayStart === -1 || jsonStart < jsonArrayStart)) {
                                    startIndex = jsonStart;
                                } else if (jsonArrayStart !== -1) {
                                    startIndex = jsonArrayStart;
                                }

                                if (startIndex !== -1) {
                                    const jsonSubstring = trimmed.substring(startIndex);
                                    try {
                                        const parsed = JSON.parse(jsonSubstring);
                                        console.log('✅ Successfully parsed JSON substring');

                                        // Check if this is a table data structure
                                        if (parsed.type === 'table') {
                                            console.log('🔧 Detected table data structure in substring, using Composite pattern');
                                            const node = this.buildNode(parsed);
                                            const tableHtml = node.render();
                                            return `<div class="table-content">${tableHtml}</div>`;
                                        }
                                        // Check if this is SVG chart data (from quick_chart tool)
                                        else if (parsed.svg_content && (parsed.success !== false)) {
                                            console.log('🎨 Detected SVG chart data, rendering SVG');
                                            const svgContent = parsed.svg_content;
                                            const chartType = parsed.chart_type || 'Chart';

                                            // Create a container for the SVG with proper styling
                                            const svgContainer = `
                                                <div class="svg-chart-content" style="
                                                    margin: 15px 0;
                                                    padding: 20px;
                                                    border: 1px solid #e0e0e0;
                                                    border-radius: 8px;
                                                    background: #fafafa;
                                                    text-align: center;
                                                    overflow: auto;
                                                ">
                                                    <div class="svg-title" style="
                                                        font-size: 16px;
                                                        color: #333;
                                                        margin-bottom: 15px;
                                                        font-weight: bold;
                                                    ">${chartType.replace('_', ' ').toUpperCase()}</div>
                                                    <div class="svg-wrapper" style="
                                                        max-width: 100%;
                                                        overflow: auto;
                                                        background: white;
                                                        border-radius: 4px;
                                                        padding: 10px;
                                                    ">${svgContent}</div>
                                                </div>`;
                                            return svgContainer;
                                        }
                                        else {
                                            const prettyJson = JSON.stringify(parsed, null, 4);
                                            const highlightedJson = this.highlightJson(prettyJson);
                                            return `<div class="json-content">${highlightedJson}</div>`;
                                        }
                                    } catch (e) {
                                        console.log('❌ JSON substring parse also failed');
                                    }
                                }
                            }
                        } else {
                            console.log('📄 Not JSON format, treating as plain text');
                        }
                    } else {
                        console.log('🔍 No text=\'...\' pattern found in content');

                        // Fallback: look for any JSON-like structure in the content
                        const jsonPattern = /(\{[^}]*\}|\[[^\]]*\])/;
                        const fallbackMatch = content.match(jsonPattern);
                        if (fallbackMatch) {
                            console.log('🔄 Found fallback JSON pattern:', fallbackMatch[1].substring(0, 100));
                            try {
                                const parsed = JSON.parse(fallbackMatch[1]);

                                // Check if this is a table data structure
                                if (parsed.type === 'table') {
                                    console.log('🔧 Detected table data structure in fallback, using Composite pattern');
                                    const node = this.buildNode(parsed);
                                    const tableHtml = node.render();
                                    return `<div class="table-content">${tableHtml}</div>`;
                                }
                                // Check if this is SVG chart data (from quick_chart tool)
                                else if (parsed.svg_content && (parsed.success !== false)) {
                                    console.log('🎨 Detected SVG chart data in fallback, rendering SVG');
                                    const svgContent = parsed.svg_content;
                                    const chartType = parsed.chart_type || 'Chart';

                                    // Create a container for the SVG with proper styling
                                    const svgContainer = `
                                        <div class="svg-chart-content" style="
                                            margin: 15px 0;
                                            padding: 20px;
                                            border: 1px solid #e0e0e0;
                                            border-radius: 8px;
                                            background: #fafafa;
                                            text-align: center;
                                            overflow: auto;
                                        ">
                                            <div class="svg-title" style="
                                                font-size: 16px;
                                                color: #333;
                                                margin-bottom: 15px;
                                                font-weight: bold;
                                            ">${chartType.replace('_', ' ').toUpperCase()}</div>
                                            <div class="svg-wrapper" style="
                                                max-width: 100%;
                                                overflow: auto;
                                                background: white;
                                                border-radius: 4px;
                                                padding: 10px;
                                            ">${svgContent}</div>
                                        </div>`;
                                    return svgContainer;
                                }
                                else {
                                    const prettyJson = JSON.stringify(parsed, null, 4);
                                    const highlightedJson = this.highlightJson(prettyJson);
                                    return `<div class="json-content">${highlightedJson}</div>`;
                                }
                            } catch (e) {
                                console.log('❌ Fallback JSON parse failed');
                            }
                        }
                    }

                    // Fallback: return as plain pre-formatted text
                    return `<pre>${this.escapeHtml(content)}</pre>`;
                } catch (e) {
                    console.log('💥 Error in formatToolResultContent:', e);
                    return `<pre>${this.escapeHtml(content)}</pre>`;
                }
            },

            highlightJson(jsonString) {
                // Simple JSON syntax highlighting with colors
                return jsonString
                    // Highlight property names (keys)
                    .replace(/("([^"\\]|\\.)*")\s*:/g, '<span class="json-key">$1</span>:')
                    // Highlight string values
                    .replace(/:\s*("([^"\\]|\\.)*")/g, ': <span class="json-string">$1</span>')
                    // Highlight boolean values
                    .replace(/:\s*(true|false)/g, ': <span class="json-boolean">$1</span>')
                    // Highlight null values
                    .replace(/:\s*(null)/g, ': <span class="json-null">$1</span>')
                    // Highlight numbers
                    .replace(/:\s*(-?\d+\.?\d*)/g, ': <span class="json-number">$1</span>')
                    // Highlight punctuation
                    .replace(/([{}[\],])/g, '<span class="json-punctuation">$1</span>');
            },

            renderJsonAsTable(tableData) {
                try {
                    console.log('🔧 renderJsonAsTable called with data:', tableData);

                    let html = '';

                    // Add title if present
                    if (tableData.title) {
                        html += `<h4 style="margin: 0 0 10px 0; color: #333; font-size: 1.1em;">${this.escapeHtml(tableData.title)}</h4>`;
                    }

                    html += '<table style="border-collapse: collapse; width: 100%; margin: 0; border: 1px solid #ddd; font-size: 0.9em;">';

                    // Add headers
                    if (tableData.columns && tableData.columns.length > 0) {
                        html += '<thead><tr>';
                        tableData.columns.forEach(header => {
                            html += `<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; text-align: left; font-weight: bold;">${this.escapeHtml(header)}</th>`;
                        });
                        html += '</tr></thead>';
                    }

                    // Add rows
                    if (tableData.rows && tableData.rows.length > 0) {
                        html += '<tbody>';
                        tableData.rows.forEach((row, rowIndex) => {
                            const bgColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';
                            html += `<tr style="background-color: ${bgColor};">`;
                            row.forEach(cell => {
                                html += `<td style="border: 1px solid #ddd; padding: 8px;">${this.escapeHtml(String(cell))}</td>`;
                            });
                            html += '</tr>';
                        });
                        html += '</tbody>';
                    }

                    html += '</table>';

                    return html;
                } catch (e) {
                    console.error('Error rendering JSON as table:', e);
                    // Fallback to JSON display
                    const prettyJson = JSON.stringify(tableData, null, 4);
                    return `<div class="json-content">${this.highlightJson(prettyJson)}</div>`;
                }
            },

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            },

            scrollToBottom() {
                this.$nextTick(() => {
                    // Scroll main chat container
                    if (this.$refs.chatContainer) {
                        this.$refs.chatContainer.scrollTop = this.$refs.chatContainer.scrollHeight;
                    }

                    // Scroll sidebar chat container using ref
                    if (this.$refs.sidebarChatContainer) {
                        this.$refs.sidebarChatContainer.scrollTop = this.$refs.sidebarChatContainer.scrollHeight;
                    }
                });
            },

            generateTitleFromMessages(messages) {
                if (!messages || !messages.length) return null;

                // Find the first user message
                const userMessages = messages.filter(msg => msg.role === 'user');
                if (!userMessages.length) return null;

                const firstUserMessage = userMessages[0].content;
                if (!firstUserMessage) return null;

                // Extract the first 4 words (or fewer if the message is shorter)
                const words = firstUserMessage.split(/\s+/).filter(word => word.trim().length > 0).slice(0, 4);
                if (!words.length) return null;

                let title = words.join(' ');

                // Add ellipsis if the message is longer than 4 words
                if (firstUserMessage.split(/\s+/).filter(word => word.trim().length > 0).length > 4) {
                    title += '...';
                }

                return title;
            },

            async deleteConversation(conversationId) {
                this.isDeletingConversation = conversationId;
                this.error = null;

                try {
                    const response = await axios.delete(`/gaia_chat/api/conversations/${conversationId}/delete/`);

                    if (response.data.success) {
                        // If the deleted conversation was the active one, clear it
                        if (this.activeConversation && this.activeConversation.id === conversationId) {
                            this.activeConversation = null;
                        }

                        // Remove the conversation from the list
                        this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
                    }
                } catch (error) {
                    console.error('Error deleting conversation:', error);
                    if (error.response && error.response.status === 403) {
                        // Permission error
                        this.error = "You do not have permission to delete this conversation";
                    } else if (error.response && error.response.status === 404) {
                        // Not found error
                        this.error = "Conversation not found";
                        // Remove it from the list anyway
                        this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
                    } else {
                        this.error = "Error deleting conversation";
                    }
                } finally {
                    this.isDeletingConversation = null;
                }
            },

            // Fix the updateConversation method to ensure proper URL formatting
            async updateConversation(conversationId, messages) {
                try {
                    // Make sure we have a valid conversation ID
                    if (!conversationId) {
                        console.error('Cannot update conversation: No conversation ID provided');
                        return false;
                    }

                    // Make sure we have messages
                    if (!messages || !Array.isArray(messages)) {
                        console.error('Cannot update conversation: Invalid messages array');
                        return false;
                    }

                    // Use the conversation ID as is, without any formatting
                    console.log(`Updating conversation with ID: ${conversationId}`);
                    console.log(`URL: /gaia_chat/api/conversations/${conversationId}/update/`);

                    // Send the update request
                    const response = await axios.post(`/gaia_chat/api/conversations/${conversationId}/update/`, {
                        messages: messages
                    });

                    console.log('Update response:', response.data);

                    return response.data.success;
                } catch (error) {
                    console.error(`Error updating conversation ${conversationId}:`, error);

                    // Log detailed error information
                    if (error.response) {
                        console.error('Response status:', error.response.status);
                        console.error('Response data:', error.response.data);
                    }

                    // If the conversation doesn't exist (404), try to create a new one
                    if (error.response && error.response.status === 404) {
                        console.log('Conversation not found, attempting to create a new one...');
                        try {
                            // Create a new conversation with the same messages
                            const createResponse = await axios.post('/gaia_chat/api/conversations/create/', {
                                title: `Conversation ${new Date().toLocaleString()}`,
                                messages: messages
                            });

                            if (createResponse.data.success) {
                                console.log('Created new conversation:', createResponse.data.conversation.id);

                                // Update the active conversation with the new one
                                this.activeConversation = createResponse.data.conversation;

                                // Update the URL while preserving the current path
                                const currentPath = window.location.pathname;
                                const newUrl = `${currentPath}?conversation=${this.activeConversation.id}`;
                                window.history.pushState({}, '', newUrl);

                                return true;
                            }
                        } catch (createError) {
                            console.error('Failed to create new conversation:', createError);
                        }
                    }

                    return false;
                }
            }
        }
    });
}
