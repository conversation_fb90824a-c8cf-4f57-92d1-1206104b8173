<!DOCTYPE html>
<html>
<head>
    <title>SVG JSON Parsing Test</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
    <div id="test-container">
        <h1>SVG JSON Parsing Test</h1>
        <div id="test-results"></div>
        <button onclick="runTest()">Run Test</button>
    </div>

    <script>
        // Simulate the problematic SVG JSON content from the error logs
        const problematicSvgJson = `{
  "success": true,
  "svg_content": "<?xml version=\\"1.0\\" encoding=\\"utf-8\\" standalone=\\"no\\"?>\\
<!DOCTYPE svg PUBLIC \\"-//W3C//DTD SVG 1.1//EN\\"\\
  \\"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\\">\\
<svg xmlns:xlink=\\"http://www.w3.org/1999/xlink\\" width=\\"856.951513pt\\" height=\\"361.605pt\\" viewBox=\\"0 0 856.**********.605\\" xmlns=\\"http://www.w3.org/2000/svg\\" version=\\"1.1\\">\\
 <metadata>\\
  <rdf:RDF xmlns:dc=\\"http://purl.org/dc/elements/1.1/\\" xmlns:cc=\\"http://creativecommons.org/ns#\\" xmlns:rdf=\\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\\">\\
   <cc:Work>\\
    <dc:type rdf:resource=\\"http://purl.org/dc/dcmitype/StillImage\\"/>\\
    <dc:date>2025-07-25T13:59:28.025450</dc:date>\\
    <dc:format>image/svg+xml</dc:format>\\
   </cc:Work>\\
  </rdf:RDF>\\
 </metadata>\\
 <defs>\\
  <style type=\\"text/css\\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\\
 </defs>\\
 <g id=\\"figure_1\\">\\
  <rect x=\\"0\\" y=\\"0\\" width=\\"856.951513\\" height=\\"361.605\\" style=\\"fill: #ffffff\\"/>\\
 </g>\\
</svg>"
}`;

        // Copy the enhanced cleaning logic from the fixed chat_app.js
        function cleanSvgJson(jsonText) {
            console.log('🔧 Testing SVG JSON cleaning...');
            console.log('📝 Original text (first 200 chars):', jsonText.substring(0, 200));

            let cleanedJson = jsonText
                // Remove line continuation backslashes that appear before newlines in SVG content
                .replace(/\?>\\\s*\n\s*<!DOCTYPE/g, '?>\n<!DOCTYPE')
                .replace(/\?>\\\s*<!DOCTYPE/g, '?><!DOCTYPE')
                // Remove line continuation backslashes at end of lines within quoted strings
                .replace(/\\(\s*\n)/g, '$1')  // Replace backslash+newline with just newline
                .replace(/\\(\s*\r\n)/g, '$1') // Replace backslash+CRLF with just CRLF
                .replace(/\\(\s*\r)/g, '$1')   // Replace backslash+CR with just CR
                // Remove any remaining line continuation backslashes at end of lines
                .replace(/\\$/gm, '')    // Remove backslash at end of line
                // Handle escaped quotes properly
                .replace(/\\\\"/g, '\\"') // Convert double-escaped quotes to single-escaped
                // Standard JSON unescaping (do this last)
                .replace(/\\n/g, '\n')
                .replace(/\\r/g, '\r')
                .replace(/\\t/g, '\t')
                .replace(/\\"/g, '"')
                .replace(/\\'/g, "'")
                .replace(/\\\\/g, '\\');

            console.log('🧹 Cleaned text (first 200 chars):', cleanedJson.substring(0, 200));
            return cleanedJson;
        }

        function runTest() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<h2>Test Results:</h2>';
            
            try {
                // Test the original problematic JSON
                console.log('Testing original problematic JSON...');
                const cleaned = cleanSvgJson(problematicSvgJson);
                const parsed = JSON.parse(cleaned.trim());
                
                if (parsed.svg_content && parsed.success) {
                    resultsDiv.innerHTML += '<p style="color: green;">✅ SUCCESS: SVG JSON parsed successfully!</p>';
                    resultsDiv.innerHTML += '<p>SVG content length: ' + parsed.svg_content.length + ' characters</p>';
                    
                    // Try to render the SVG
                    const svgContainer = `
                        <div style="
                            margin: 15px 0;
                            padding: 20px;
                            border: 1px solid #e0e0e0;
                            border-radius: 8px;
                            background: #fafafa;
                            text-align: center;
                        ">
                            <div style="
                                font-size: 16px;
                                color: #333;
                                margin-bottom: 15px;
                                font-weight: bold;
                            ">Test SVG Chart</div>
                            <div style="
                                max-width: 100%;
                                overflow: auto;
                                background: white;
                                border-radius: 4px;
                                padding: 10px;
                            ">${parsed.svg_content}</div>
                        </div>`;
                    
                    resultsDiv.innerHTML += svgContainer;
                } else {
                    resultsDiv.innerHTML += '<p style="color: orange;">⚠️ WARNING: JSON parsed but no valid SVG content found</p>';
                }
            } catch (e) {
                resultsDiv.innerHTML += '<p style="color: red;">❌ ERROR: ' + e.message + '</p>';
                console.error('Test failed:', e);
                
                // Try aggressive cleaning
                try {
                    console.log('Trying aggressive cleaning...');
                    let aggressiveClean = problematicSvgJson
                        // Remove line continuation backslashes more aggressively
                        .replace(/\\(\s*\n)/g, '$1')  // Replace backslash+newline with just newline
                        .replace(/\\(\s*\r\n)/g, '$1') // Replace backslash+CRLF with just CRLF
                        .replace(/\\(\s*\r)/g, '$1')   // Replace backslash+CR with just CR
                        .replace(/\\$/gm, '')    // Remove backslash at end of line
                        .replace(/\\(?=\s*[<>])/g, '') // Remove backslash before XML angle brackets
                        .replace(/\\(?![nrt"'\\\/])/g, '') // Remove invalid escape sequences
                        .replace(/\\"/g, '"')
                        .replace(/\\'/g, "'")
                        .replace(/\\n/g, '\n')
                        .replace(/\\r/g, '\r')
                        .replace(/\\t/g, '\t')
                        .replace(/\\\\/g, '\\');
                    
                    const parsed = JSON.parse(aggressiveClean.trim());
                    if (parsed.svg_content && parsed.success) {
                        resultsDiv.innerHTML += '<p style="color: blue;">✅ SUCCESS with aggressive cleaning!</p>';
                    }
                } catch (e2) {
                    resultsDiv.innerHTML += '<p style="color: red;">❌ Aggressive cleaning also failed: ' + e2.message + '</p>';
                }
            }
        }
    </script>
</body>
</html>
