#!/usr/bin/env python3
"""
Sprite wrapper for Simple Chart ELF

Provides intelligent chart generation from data tables with automatic chart type selection.
Supports bubble charts, bar charts, and time series charts.
"""

import os
import sys
import traceback
from typing import Dict, Any, Optional

# Add the project root to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..')
gaia_root = os.path.join(current_dir, '..', '..')
if project_root not in sys.path:
    sys.path.insert(0, project_root)
if gaia_root not in sys.path:
    sys.path.insert(0, gaia_root)

try:
    from gaia.gaia_elf.chart_elf.simple_chart_elf import simple_chart
except ImportError as e:
    print(f"Warning: Could not import simple_chart_elf: {e}")
    simple_chart = None


def simple_chart_svg(data_table: str, chart_spec: str) -> Dict[str, Any]:
    """
    Generate an SVG chart from data table with intelligent chart type selection.
    
    Args:
        data_table: String representation of data table (CSV-like format)
        chart_spec: Chart specification/requirements (e.g., "bubble chart showing GDP vs population")
    
    Returns:
        dict: {
            "success": bool,
            "svg_content": str,  # SVG content as string
            "chart_type": str,   # Detected chart type
            "error": str         # Error message if failed
        }
    """
    if simple_chart is None:
        return {
            "success": False,
            "svg_content": "",
            "chart_type": "",
            "error": "simple_chart_elf module not available"
        }
    
    try:
        result = simple_chart(
            input_data_table_str=data_table,
            input_chart_spec_str=chart_spec,
            output_format="svg"
        )
        
        # Debug logging
        print(f"DEBUG: Chart generation result keys: {result.keys()}")
        print(f"DEBUG: Chart generation success: {result.get('success', 'N/A')}")
        print(f"DEBUG: SVG content length: {len(result.get('content', ''))}")
        print(f"DEBUG: SVG content preview: {result.get('content', '')[:200]}...")

        return {
            "success": True,
            "svg_content": result["content"],
            "chart_type": result["chart_type"],
            "error": ""
        }
        
    except Exception as e:
        return {
            "success": False,
            "svg_content": "",
            "chart_type": "",
            "error": f"Chart generation failed: {str(e)}"
        }


def simple_chart_png(data_table: str, chart_spec: str) -> Dict[str, Any]:
    """
    Generate a PNG chart file from data table with intelligent chart type selection.
    
    Args:
        data_table: String representation of data table (CSV-like format)
        chart_spec: Chart specification/requirements (e.g., "bar chart of sales by region")
    
    Returns:
        dict: {
            "success": bool,
            "png_path": str,     # Full path to generated PNG file
            "chart_type": str,   # Detected chart type
            "error": str         # Error message if failed
        }
    """
    if simple_chart is None:
        return {
            "success": False,
            "png_path": "",
            "chart_type": "",
            "error": "simple_chart_elf module not available"
        }
    
    try:
        result = simple_chart(
            input_data_table_str=data_table,
            input_chart_spec_str=chart_spec,
            output_format="png"
        )
        
        return {
            "success": True,
            "png_path": result["content"],
            "chart_type": result["chart_type"],
            "error": ""
        }
        
    except Exception as e:
        return {
            "success": False,
            "png_path": "",
            "chart_type": "",
            "error": f"Chart generation failed: {str(e)}"
        }


def quick_chart(data_table: str, chart_type: str = "auto") -> Dict[str, Any]:
    """
    Quick chart generation with minimal configuration.
    
    Args:
        data_table: String representation of data table
        chart_type: "auto", "bubble", "bar", or "timeseries"
    
    Returns:
        dict: {
            "success": bool,
            "svg_content": str,
            "chart_type": str,
            "error": str
        }
    """
    # Map simplified chart types to specifications
    chart_specs = {
        "bubble": "Create a bubble chart with appropriate dimensions",
        "bar": "Create a bar chart showing the data",
        "timeseries": "Create a time series chart if temporal data is present",
        "auto": "Create the most appropriate chart type for this data"
    }
    
    spec = chart_specs.get(chart_type, chart_specs["auto"])
    return simple_chart_svg(data_table, spec)


# Test function for development
def test_simple_chart():
    """Test the simple chart functionality with sample data."""
    sample_data = """Country,GDP_per_capita,Population_millions
USA,65000,331
Germany,46000,83
Japan,40000,125
UK,42000,67
France,39000,68"""
    
    chart_spec = "Create a bubble chart showing countries by GDP per capita and population"
    
    result = simple_chart_svg(sample_data, chart_spec)
    
    if result["success"]:
        print(f"✅ Chart generated successfully!")
        print(f"Chart type: {result['chart_type']}")
        print(f"SVG length: {len(result['svg_content'])} characters")
        
        # Save to file for inspection
        with open("/tmp/test_chart.svg", "w") as f:
            f.write(result["svg_content"])
        print("📁 Chart saved to /tmp/test_chart.svg")
    else:
        print(f"❌ Chart generation failed: {result['error']}")


if __name__ == "__main__":
    test_simple_chart()
